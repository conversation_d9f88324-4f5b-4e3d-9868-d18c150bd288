package com.futures.function;

import com.futures.pojo.BestBidOfferEvent;
import com.futures.pojo.OrderBookSnapshot;
import org.apache.flink.api.common.state.ValueState;
import org.apache.flink.api.common.state.ValueStateDescriptor;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.KeyedProcessFunction;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * BBO提取器函数
 * 从订单簿快照中提取最优买卖价信息，生成BBO事件用于广播
 * 
 * 功能：
 * 1. 监控订单簿快照变化
 * 2. 提取最优买卖价信息
 * 3. 检测价格变化，只在BBO发生变化时输出
 * 4. 支持去重，避免重复广播相同的BBO信息
 */
public class BBOExtractorFunction extends KeyedProcessFunction<String, OrderBookSnapshot, BestBidOfferEvent> {
    
    private static final Logger logger = LoggerFactory.getLogger(BBOExtractorFunction.class);
    
    // 状态：上一次的BBO信息，用于变化检测
    private transient ValueState<BestBidOfferEvent> lastBBOState;
    
    // 状态：BBO更新计数器
    private transient ValueState<Long> updateCounterState;
    
    @Override
    public void open(Configuration parameters) throws Exception {
        // 初始化上一次BBO状态
        ValueStateDescriptor<BestBidOfferEvent> lastBBODescriptor = 
            new ValueStateDescriptor<>(
                "last-bbo-state",
                BestBidOfferEvent.class
            );
        lastBBOState = getRuntimeContext().getState(lastBBODescriptor);
        
        // 初始化更新计数器
        ValueStateDescriptor<Long> counterDescriptor = 
            new ValueStateDescriptor<>(
                "update-counter",
                Long.class,
                0L
            );
        updateCounterState = getRuntimeContext().getState(counterDescriptor);
    }
    
    @Override
    public void processElement(OrderBookSnapshot snapshot, Context ctx, Collector<BestBidOfferEvent> out) throws Exception {
        String contractCode = snapshot.getContract_cde();
        
        // 从订单簿快照提取BBO信息
        BestBidOfferEvent currentBBO = BestBidOfferEvent.fromOrderBookSnapshot(snapshot);
        
        // 获取上一次的BBO信息
        BestBidOfferEvent lastBBO = lastBBOState.value();
        
        // 检查BBO是否发生变化
        boolean shouldEmit = shouldEmitBBO(currentBBO, lastBBO);
        
        if (shouldEmit) {
            // 设置更新序列号
            Long updateCounter = updateCounterState.value();
            updateCounter++;
            currentBBO.setUpdateSequence(updateCounter);
            updateCounterState.update(updateCounter);
            
            // 输出BBO事件
            out.collect(currentBBO);
            
            // 更新状态
            lastBBOState.update(currentBBO);
            
            logger.debug("BBO变化检测: 合约={}, 新BBO={}, 更新序列={}", 
                contractCode, currentBBO, updateCounter);
        } else {
            logger.trace("BBO无变化: 合约={}, 跳过输出", contractCode);
        }
    }
    
    /**
     * 判断是否应该输出BBO事件
     * 只有在BBO发生实质性变化时才输出，避免重复广播
     */
    private boolean shouldEmitBBO(BestBidOfferEvent currentBBO, BestBidOfferEvent lastBBO) {
        // 如果是第一次或者上次BBO为空，则输出
        if (lastBBO == null) {
            return currentBBO.isValid();
        }
        
        // 如果当前BBO无效，但上次有效，则输出（表示市场清空）
        if (!currentBBO.isValid() && lastBBO.isValid()) {
            return true;
        }
        
        // 如果当前BBO有效，但上次无效，则输出（表示市场恢复）
        if (currentBBO.isValid() && !lastBBO.isValid()) {
            return true;
        }
        
        // 如果两次都无效，则不输出
        if (!currentBBO.isValid() && !lastBBO.isValid()) {
            return false;
        }
        
        // 检查买价变化
        boolean bidChanged = !equalsPrices(currentBBO.getBestBid(), lastBBO.getBestBid()) ||
                           !equalsVolumes(currentBBO.getBestBidVolume(), lastBBO.getBestBidVolume());
        
        // 检查卖价变化
        boolean askChanged = !equalsPrices(currentBBO.getBestAsk(), lastBBO.getBestAsk()) ||
                           !equalsVolumes(currentBBO.getBestAskVolume(), lastBBO.getBestAskVolume());
        
        return bidChanged || askChanged;
    }
    
    /**
     * 比较两个价格是否相等（考虑精度问题）
     */
    private boolean equalsPrices(Double price1, Double price2) {
        if (price1 == null && price2 == null) {
            return true;
        }
        if (price1 == null || price2 == null) {
            return false;
        }
        
        // 使用小的容差来比较浮点数
        final double EPSILON = 1e-6;
        return Math.abs(price1 - price2) < EPSILON;
    }
    
    /**
     * 比较两个数量是否相等
     */
    private boolean equalsVolumes(Long volume1, Long volume2) {
        if (volume1 == null && volume2 == null) {
            return true;
        }
        if (volume1 == null || volume2 == null) {
            return false;
        }
        return volume1.equals(volume2);
    }
}
