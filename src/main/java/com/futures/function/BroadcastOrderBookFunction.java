package com.futures.function;

import com.futures.pojo.*;
import com.futures.function.CombinationOrderSplitter.CombinationOrderLegInfo;
import org.apache.flink.api.common.state.*;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.co.KeyedBroadcastProcessFunction;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

/**
 * 基于广播状态的订单簿重建函数
 * 解决虚拟层（组合委托）订单簿构建依赖基础层（单腿委托）实时价格的问题
 * 
 * 架构设计：
 * - 主流：所有订单事件（单腿+组合）
 * - 广播流：BBO（最优买卖价）更新事件
 * - 广播状态：全市场所有合约的实时最优价格
 * - 本地状态：每个合约的订单簿状态
 */
public class BroadcastOrderBookFunction extends KeyedBroadcastProcessFunction<String, Object, BestBidOfferEvent, OrderBookSnapshot> {
    
    private static final Logger logger = LoggerFactory.getLogger(BroadcastOrderBookFunction.class);
    private static final long SNAPSHOT_INTERVAL_MS = 500; // 0.5秒快照间隔
    
    // 广播状态描述符：全市场BBO价格
    public static final MapStateDescriptor<String, BestBidOfferEvent> BBO_BROADCAST_STATE_DESC = 
        new MapStateDescriptor<>(
            "bbo-broadcast-state",
            String.class,
            BestBidOfferEvent.class
        );
    
    // 本地状态：基础层订单簿（单腿订单）
    private transient MapState<String, SingleLegOrderEvent> baseLayerOrders;
    
    // 本地状态：虚拟层订单簿（组合订单腿）
    private transient MapState<String, CombinationOrderLegInfo> virtualLayerOrders;
    
    // 本地状态：组合订单状态追踪
    private transient MapState<String, com.futures.pojo.CombinationOrderState> combOrderStates;
    
    // 本地状态：订单状态时间戳（用于时序验证）
    private transient MapState<String, Long> orderStateTimestamps;

    @Override
    public void open(Configuration parameters) throws Exception {
        // 初始化基础层订单状态
        MapStateDescriptor<String, SingleLegOrderEvent> baseLayerDescriptor = 
            new MapStateDescriptor<>(
                "base-layer-orders",
                String.class,
                SingleLegOrderEvent.class
            );
        baseLayerOrders = getRuntimeContext().getMapState(baseLayerDescriptor);
        
        // 初始化虚拟层订单状态
        MapStateDescriptor<String, CombinationOrderLegInfo> virtualLayerDescriptor = 
            new MapStateDescriptor<>(
                "virtual-layer-orders",
                String.class,
                CombinationOrderLegInfo.class
            );
        virtualLayerOrders = getRuntimeContext().getMapState(virtualLayerDescriptor);
        
        // 初始化组合订单状态追踪
        MapStateDescriptor<String, com.futures.pojo.CombinationOrderState> combStateDescriptor =
            new MapStateDescriptor<>(
                "combination-order-states",
                String.class,
                com.futures.pojo.CombinationOrderState.class
            );
        combOrderStates = getRuntimeContext().getMapState(combStateDescriptor);
        
        // 初始化订单状态时间戳
        MapStateDescriptor<String, Long> timestampDescriptor = 
            new MapStateDescriptor<>(
                "order-state-timestamps",
                String.class,
                Long.class
            );
        orderStateTimestamps = getRuntimeContext().getMapState(timestampDescriptor);
    }

    @Override
    public void processElement(Object value, ReadOnlyContext ctx, Collector<OrderBookSnapshot> out) throws Exception {
        String contractCode = ctx.getCurrentKey();
        long currentTime = ctx.timestamp();
        
        logger.debug("处理订单事件: 合约={}, 事件类型={}, 时间戳={}", 
            contractCode, value.getClass().getSimpleName(), currentTime);
        
        try {
            if (value instanceof SingleLegOrderEvent) {
                processBaseLayerOrder((SingleLegOrderEvent) value, ctx);
            } else if (value instanceof CombinationOrderEvent) {
                processCombinationOrder((CombinationOrderEvent) value, ctx);
            } else if (value instanceof CombinationOrderLegInfo) {
                processVirtualLayerOrder((CombinationOrderLegInfo) value, ctx);
            }
            
            // 注册定时器生成快照
            registerSnapshotTimers(currentTime, ctx);
            
        } catch (Exception e) {
            logger.error("处理订单事件失败: 合约={}, 事件={}", contractCode, value.toString(), e);
        }
    }

    @Override
    public void processBroadcastElement(BestBidOfferEvent bboEvent, Context ctx, Collector<OrderBookSnapshot> out) throws Exception {
        // 更新广播状态中的BBO信息
        BroadcastState<String, BestBidOfferEvent> bboState = ctx.getBroadcastState(BBO_BROADCAST_STATE_DESC);
        bboState.put(bboEvent.getContractCode(), bboEvent);
        
        logger.debug("更新广播BBO: 合约={}, 最优买价={}, 最优卖价={}", 
            bboEvent.getContractCode(), bboEvent.getBestBid(), bboEvent.getBestAsk());
    }

    @Override
    public void onTimer(long timestamp, OnTimerContext ctx, Collector<OrderBookSnapshot> out) throws Exception {
        String contractCode = ctx.getCurrentKey();
        
        // 构建基础层订单簿
        Map<Double, Long> baseLayerBids = new TreeMap<>(Collections.reverseOrder());
        Map<Double, Long> baseLayerAsks = new TreeMap<>();
        
        buildBaseLayerOrderBook(baseLayerBids, baseLayerAsks, timestamp);
        
        // 构建虚拟层订单簿（使用广播状态中的BBO信息）
        Map<Double, Long> virtualLayerBids = new TreeMap<>(Collections.reverseOrder());
        Map<Double, Long> virtualLayerAsks = new TreeMap<>();
        
        buildVirtualLayerOrderBook(virtualLayerBids, virtualLayerAsks, ctx, timestamp);
        
        // 合并基础层和虚拟层
        Map<Double, Long> finalBids = new TreeMap<>(Collections.reverseOrder());
        Map<Double, Long> finalAsks = new TreeMap<>();
        
        finalBids.putAll(baseLayerBids);
        finalAsks.putAll(baseLayerAsks);
        
        // 合并虚拟层
        for (Map.Entry<Double, Long> entry : virtualLayerBids.entrySet()) {
            finalBids.merge(entry.getKey(), entry.getValue(), Long::sum);
        }
        for (Map.Entry<Double, Long> entry : virtualLayerAsks.entrySet()) {
            finalAsks.merge(entry.getKey(), entry.getValue(), Long::sum);
        }
        
        // 输出快照
        OrderBookSnapshot snapshot = new OrderBookSnapshot(contractCode, timestamp);
        snapshot.setBids(finalBids);
        snapshot.setAsks(finalAsks);
        
        out.collect(snapshot);
        
        logger.debug("输出分层订单簿快照: 合约={}, 基础层({}/{}档), 虚拟层({}/{}档), 最终({}/{}档)",
            contractCode, 
            baseLayerBids.size(), baseLayerAsks.size(),
            virtualLayerBids.size(), virtualLayerAsks.size(),
            finalBids.size(), finalAsks.size());
        
        // 注册下一个定时器
        registerNextTimer(timestamp, ctx);
    }

    /**
     * 处理基础层单腿订单
     */
    private void processBaseLayerOrder(SingleLegOrderEvent order, ReadOnlyContext ctx) throws Exception {
        String orderNumber = order.getOrd_nbr();
        String orderStatus = order.getOrd_sts();
        
        // 记录订单状态时间戳
        orderStateTimestamps.put(orderNumber, ctx.timestamp());
        
        logger.debug("处理基础层订单: 订单号={}, 状态={}, 剩余量={}", 
            orderNumber, orderStatus, order.getRmn_vol());
        
        if (orderStatus == null || orderStatus.isEmpty()) {
            return;
        }
        
        int status = (int) Double.parseDouble(orderStatus);
        
        switch (status) {
            case 0: // 全部成交
            case 5: // 已撤销
                baseLayerOrders.remove(orderNumber);
                if (status == 5) {
                    orderStateTimestamps.put(orderNumber + "_CANCELLED", ctx.timestamp());
                }
                break;
                
            case 1: // 部分成交在队列中
            case 3: // 未成交在队列中
                if (order.getRmn_vol() > 0) {
                    baseLayerOrders.put(orderNumber, order);
                } else {
                    baseLayerOrders.remove(orderNumber);
                }
                break;
                
            default:
                baseLayerOrders.remove(orderNumber);
                break;
        }
    }

    /**
     * 处理组合订单（拆分为两腿）
     */
    private void processCombinationOrder(CombinationOrderEvent combOrder, ReadOnlyContext ctx) throws Exception {
        logger.info("处理组合订单: 订单号={}, 腿1={}, 腿2={}", 
            combOrder.getOrd_nbr(), combOrder.getLeg_1_contract_cde(), combOrder.getLeg_2_contract_cde());
        
        // 创建两腿信息
        CombinationOrderLegInfo leg1Info = new CombinationOrderLegInfo(
            combOrder.getOrd_nbr(),
            combOrder.getLeg_1_contract_cde(),
            combOrder.getLeg_2_contract_cde(),
            combOrder.getB_s_tag(),
            combOrder.getOrd_prc(),
            combOrder.getRmn_vol(),
            combOrder.getOrd_sts(),
            combOrder.getEventTimestamp(),
            1,
            combOrder.getTrd_vol()
        );
        
        String oppositeBuySellTag = "B".equals(combOrder.getB_s_tag()) ? "S" : "B";
        CombinationOrderLegInfo leg2Info = new CombinationOrderLegInfo(
            combOrder.getOrd_nbr(),
            combOrder.getLeg_2_contract_cde(),
            combOrder.getLeg_1_contract_cde(),
            oppositeBuySellTag,
            -combOrder.getOrd_prc(),
            combOrder.getRmn_vol(),
            combOrder.getOrd_sts(),
            combOrder.getEventTimestamp(),
            2,
            combOrder.getTrd_vol()
        );
        
        // 处理两腿（注意：这里需要将腿信息发送到对应的合约处理器）
        // 由于当前函数只处理当前合约，我们只处理属于当前合约的腿
        String currentContract = ctx.getCurrentKey();
        if (currentContract.equals(leg1Info.getCurrentLegContract())) {
            processVirtualLayerOrder(leg1Info, ctx);
        }
        if (currentContract.equals(leg2Info.getCurrentLegContract())) {
            processVirtualLayerOrder(leg2Info, ctx);
        }
    }

    /**
     * 处理虚拟层组合订单腿
     */
    private void processVirtualLayerOrder(CombinationOrderLegInfo legInfo, ReadOnlyContext ctx) throws Exception {
        String orderNumber = legInfo.getOrdNbr();
        String legKey = orderNumber + "_leg" + legInfo.getLegNumber();
        String orderStatus = legInfo.getOrderStatus();
        
        logger.debug("处理虚拟层订单腿: 订单号={}, 腿号={}, 状态={}, 剩余量={}", 
            orderNumber, legInfo.getLegNumber(), orderStatus, legInfo.getRemainingVol());
        
        // 更新组合订单状态
        updateCombinationOrderState(legInfo, ctx);
        
        // 管理虚拟层订单
        switch (orderStatus) {
            case "0": // 全部成交
            case "5": // 已撤销
                virtualLayerOrders.remove(legKey);
                break;
                
            case "1": // 部分成交在队列中
            case "3": // 未成交在队列中
                if (legInfo.getRemainingVol() > 0) {
                    virtualLayerOrders.put(legKey, legInfo);
                } else {
                    virtualLayerOrders.remove(legKey);
                }
                break;
                
            default:
                virtualLayerOrders.remove(legKey);
                break;
        }
    }

    /**
     * 构建基础层订单簿
     */
    private void buildBaseLayerOrderBook(Map<Double, Long> bids, Map<Double, Long> asks, long timestamp) throws Exception {
        for (SingleLegOrderEvent order : baseLayerOrders.values()) {
            if (order.getRmn_vol() > 0 && isOrderValidAtTime(order.getOrd_nbr(), timestamp)) {
                if ("B".equals(order.getB_s_tag())) {
                    bids.merge(order.getOrd_prc(), order.getRmn_vol(), Long::sum);
                } else if ("S".equals(order.getB_s_tag())) {
                    asks.merge(order.getOrd_prc(), order.getRmn_vol(), Long::sum);
                }
            }
        }
    }

    /**
     * 构建虚拟层订单簿（使用广播状态中的BBO信息）
     */
    private void buildVirtualLayerOrderBook(Map<Double, Long> virtualBids, Map<Double, Long> virtualAsks, 
                                          OnTimerContext ctx, long timestamp) throws Exception {
        
        ReadOnlyBroadcastState<String, BestBidOfferEvent> bboState = ctx.getBroadcastState(BBO_BROADCAST_STATE_DESC);
        
        for (CombinationOrderLegInfo legInfo : virtualLayerOrders.values()) {
            if (legInfo.getRemainingVol() > 0) {
                // 获取对手腿合约的BBO信息
                String oppositeLegContract = legInfo.getOppositeLegContract();
                BestBidOfferEvent oppositeBBO = bboState.get(oppositeLegContract);
                
                // 生成虚拟挂单
                generateVirtualOrders(virtualBids, virtualAsks, legInfo, oppositeBBO);
            }
        }
    }

    /**
     * 生成虚拟挂单（基于对手腿的BBO信息）
     */
    private void generateVirtualOrders(Map<Double, Long> virtualBids, Map<Double, Long> virtualAsks,
                                     CombinationOrderLegInfo legInfo, BestBidOfferEvent oppositeBBO) {
        
        if (oppositeBBO == null) {
            logger.debug("对手腿合约 {} 无BBO信息，跳过虚拟挂单生成", legInfo.getOppositeLegContract());
            return;
        }
        
        String buySellTag = legInfo.getBuySellTag();
        long volume = legInfo.getRemainingVol();
        double spreadPrice = legInfo.getSpreadPrice();
        
        final double MIN_TICK = 0.01;
        
        if ("B".equals(buySellTag)) { // 买腿
            // 基于对手腿卖价和价差计算虚拟买价
            if (oppositeBBO.getBestAsk() != null) {
                double virtualBidPrice = oppositeBBO.getBestAsk() + spreadPrice - MIN_TICK;
                if (virtualBidPrice > MIN_TICK) {
                    virtualBids.merge(virtualBidPrice, volume, Long::sum);
                    logger.debug("生成虚拟买单: 价格={}, 数量={}, 基于对手腿卖价={}, 价差={}", 
                        virtualBidPrice, volume, oppositeBBO.getBestAsk(), spreadPrice);
                }
            }
        } else if ("S".equals(buySellTag)) { // 卖腿
            // 基于对手腿买价和价差计算虚拟卖价
            if (oppositeBBO.getBestBid() != null) {
                double virtualAskPrice = oppositeBBO.getBestBid() + spreadPrice + MIN_TICK;
                if (virtualAskPrice > MIN_TICK) {
                    virtualAsks.merge(virtualAskPrice, volume, Long::sum);
                    logger.debug("生成虚拟卖单: 价格={}, 数量={}, 基于对手腿买价={}, 价差={}", 
                        virtualAskPrice, volume, oppositeBBO.getBestBid(), spreadPrice);
                }
            }
        }
    }

    /**
     * 更新组合订单状态
     */
    private void updateCombinationOrderState(CombinationOrderLegInfo legInfo, ReadOnlyContext ctx) throws Exception {
        String orderNumber = legInfo.getOrdNbr();

        com.futures.pojo.CombinationOrderState combState = combOrderStates.get(orderNumber);
        if (combState == null) {
            combState = new com.futures.pojo.CombinationOrderState(
                orderNumber,
                legInfo.getCurrentLegContract(),
                legInfo.getOppositeLegContract(),
                legInfo.getBuySellTag(),
                legInfo.getSpreadPrice(),
                legInfo.getRemainingVol()
            );
        }

        // 计算本轮成交量
        long previousRemaining = combState.getCurrentRemainingVol();
        long currentRemaining = legInfo.getRemainingVol();
        long tradedThisRound = Math.max(0, previousRemaining - currentRemaining);

        // 更新状态
        combState.updateState(legInfo.getOrderStatus(), tradedThisRound, currentRemaining, legInfo.getEventTimestamp());
        combOrderStates.put(orderNumber, combState);

        if (tradedThisRound > 0) {
            logger.info("组合订单成交: 订单号={}, 本轮成交量={}, 累计成交量={}, 完成度={:.1f}%",
                orderNumber, tradedThisRound, combState.getTotalTradedVol(),
                combState.getCompletionRate() * 100);
        }
    }

    /**
     * 检查订单在指定时间点是否有效
     */
    private boolean isOrderValidAtTime(String orderNumber, long timestamp) throws Exception {
        Long orderCreateTime = orderStateTimestamps.get(orderNumber);
        Long orderCancelTime = orderStateTimestamps.get(orderNumber + "_CANCELLED");

        // 检查订单是否在时间窗口内有效
        if (orderCreateTime != null && timestamp < orderCreateTime) {
            return false;
        }

        if (orderCancelTime != null && timestamp >= orderCancelTime) {
            return false;
        }

        return true;
    }

    /**
     * 注册快照定时器
     */
    private void registerSnapshotTimers(long currentTime, ReadOnlyContext ctx) throws Exception {
        if (currentTime <= 0) {
            currentTime = System.currentTimeMillis();
        }

        // 注册未来几个窗口的定时器
        long startWindow = ((currentTime / SNAPSHOT_INTERVAL_MS) + 1) * SNAPSHOT_INTERVAL_MS;

        for (int i = 0; i < 12; i++) {
            long timerTime = startWindow + (i * SNAPSHOT_INTERVAL_MS);
            ctx.timerService().registerEventTimeTimer(timerTime);
        }

        logger.debug("注册快照定时器: 当前时间={}, 窗口范围={} 到 {}",
            currentTime, startWindow, startWindow + 11 * SNAPSHOT_INTERVAL_MS);
    }

    /**
     * 注册下一个定时器
     */
    private void registerNextTimer(long timestamp, OnTimerContext ctx) throws Exception {
        long currentWatermark = ctx.timerService().currentWatermark();
        boolean isDataStreamEnded = currentWatermark >= Long.MAX_VALUE;

        if (!isDataStreamEnded) {
            long nextTimerTime = timestamp + SNAPSHOT_INTERVAL_MS;
            ctx.timerService().registerEventTimeTimer(nextTimerTime);
            logger.debug("注册下次定时器: {}", nextTimerTime);
        }
    }

    /**
     * 格式化时间戳
     */
    private String formatTimestamp(long timestamp) {
        return java.time.LocalDateTime.ofInstant(
            java.time.Instant.ofEpochMilli(timestamp),
            java.time.ZoneId.of("Asia/Shanghai")
        ).format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS"));
    }
}
