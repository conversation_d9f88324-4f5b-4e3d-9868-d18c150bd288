package com.futures.pojo;

// 导入CombinationOrderState类（从OrderBookReconstructionFunction中复制）
public class CombinationOrderState implements java.io.Serializable {
    private static final long serialVersionUID = 1L;

    private String ordNbr;
    private String leg1Contract;
    private String leg2Contract;
    private String buySellTag;
    private double orderPrice;
    private long originalVol;
    private long currentRemainingVol;
    private long totalTradedVol;
    private String currentStatus;
    private int roundCount;
    private long lastUpdateTimestamp;
    private boolean enableVirtualOrders = true;
    private double virtualOrderSpread = 0.5;

    public CombinationOrderState(String ordNbr, String leg1Contract, String leg2Contract,
                                 String buySellTag, double orderPrice, long originalVol) {
        this.ordNbr = ordNbr;
        this.leg1Contract = leg1Contract;
        this.leg2Contract = leg2Contract;
        this.buySellTag = buySellTag;
        this.orderPrice = orderPrice;
        this.originalVol = originalVol;
        this.currentRemainingVol = originalVol;
        this.totalTradedVol = 0;
        this.currentStatus = "3";
        this.roundCount = 0;
        this.lastUpdateTimestamp = 0L;
    }

    public void updateState(String newStatus, long tradedVol, long remainingVol, long eventTimestamp) {
        if (tradedVol > 0) {
            this.roundCount++;
            this.totalTradedVol += tradedVol;
        }
        this.currentStatus = newStatus;
        this.currentRemainingVol = remainingVol;
        this.lastUpdateTimestamp = eventTimestamp;
    }

    public boolean shouldGenerateVirtualOrders() {
        return enableVirtualOrders &&
                ("1".equals(currentStatus) || "3".equals(currentStatus)) &&
                currentRemainingVol > 0;
    }

    public double getCompletionRate() {
        if (originalVol == 0) return 0.0;
        return (double) totalTradedVol / originalVol;
    }

    // Getters
    public String getOrdNbr() {
        return ordNbr;
    }

    public String getLeg1Contract() {
        return leg1Contract;
    }

    public String getLeg2Contract() {
        return leg2Contract;
    }

    public String getBuySellTag() {
        return buySellTag;
    }

    public double getOrderPrice() {
        return orderPrice;
    }

    public long getOriginalVol() {
        return originalVol;
    }

    public long getCurrentRemainingVol() {
        return currentRemainingVol;
    }

    public long getTotalTradedVol() {
        return totalTradedVol;
    }

    public String getCurrentStatus() {
        return currentStatus;
    }

    public int getRoundCount() {
        return roundCount;
    }

    public long getLastUpdateTimestamp() {
        return lastUpdateTimestamp;
    }

    public double getVirtualOrderSpread() {
        return virtualOrderSpread;
    }
}
