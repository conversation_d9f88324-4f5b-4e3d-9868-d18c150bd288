package com.futures.pojo;

import java.io.Serializable;

/**
 * 最优买卖价事件（BBO - Best Bid Offer）
 * 用于广播状态，提供全市场合约的实时最优价格信息
 */
public class BestBidOfferEvent implements Serializable {
    private static final long serialVersionUID = 1L;
    
    private String contractCode;      // 合约代码
    private Double bestBid;          // 最优买价
    private Double bestAsk;          // 最优卖价
    private Long bestBidVolume;      // 最优买价数量
    private Long bestAskVolume;      // 最优卖价数量
    private long timestamp;          // 事件时间戳
    private long updateSequence;     // 更新序列号，用于处理乱序
    
    public BestBidOfferEvent() {}
    
    public BestBidOfferEvent(String contractCode, Double bestBid, Double bestAsk, 
                           Long bestBidVolume, Long bestAskVolume, long timestamp) {
        this.contractCode = contractCode;
        this.bestBid = bestBid;
        this.bestAsk = bestAsk;
        this.bestBidVolume = bestBidVolume;
        this.bestAskVolume = bestAskVolume;
        this.timestamp = timestamp;
        this.updateSequence = System.nanoTime(); // 使用纳秒时间作为序列号
    }
    
    /**
     * 从订单簿快照创建BBO事件
     */
    public static BestBidOfferEvent fromOrderBookSnapshot(OrderBookSnapshot snapshot) {
        Double bestBid = snapshot.getBestBid();
        Double bestAsk = snapshot.getBestAsk();
        
        Long bestBidVolume = null;
        Long bestAskVolume = null;
        
        if (bestBid != null && !snapshot.getBids().isEmpty()) {
            bestBidVolume = snapshot.getBids().get(bestBid);
        }
        
        if (bestAsk != null && !snapshot.getAsks().isEmpty()) {
            bestAskVolume = snapshot.getAsks().get(bestAsk);
        }
        
        return new BestBidOfferEvent(
            snapshot.getContract_cde(),
            bestBid,
            bestAsk,
            bestBidVolume,
            bestAskVolume,
            snapshot.getTimestamp()
        );
    }
    
    /**
     * 检查BBO是否有效（至少有买价或卖价）
     */
    public boolean isValid() {
        return bestBid != null || bestAsk != null;
    }
    
    /**
     * 获取买卖价差
     */
    public Double getSpread() {
        if (bestBid != null && bestAsk != null) {
            return bestAsk - bestBid;
        }
        return null;
    }
    
    /**
     * 获取中间价
     */
    public Double getMidPrice() {
        if (bestBid != null && bestAsk != null) {
            return (bestBid + bestAsk) / 2.0;
        }
        return null;
    }
    
    // Getter和Setter方法
    public String getContractCode() {
        return contractCode;
    }
    
    public void setContractCode(String contractCode) {
        this.contractCode = contractCode;
    }
    
    public Double getBestBid() {
        return bestBid;
    }
    
    public void setBestBid(Double bestBid) {
        this.bestBid = bestBid;
    }
    
    public Double getBestAsk() {
        return bestAsk;
    }
    
    public void setBestAsk(Double bestAsk) {
        this.bestAsk = bestAsk;
    }
    
    public Long getBestBidVolume() {
        return bestBidVolume;
    }
    
    public void setBestBidVolume(Long bestBidVolume) {
        this.bestBidVolume = bestBidVolume;
    }
    
    public Long getBestAskVolume() {
        return bestAskVolume;
    }
    
    public void setBestAskVolume(Long bestAskVolume) {
        this.bestAskVolume = bestAskVolume;
    }
    
    public long getTimestamp() {
        return timestamp;
    }
    
    public void setTimestamp(long timestamp) {
        this.timestamp = timestamp;
    }
    
    public long getUpdateSequence() {
        return updateSequence;
    }
    
    public void setUpdateSequence(long updateSequence) {
        this.updateSequence = updateSequence;
    }
    
    @Override
    public String toString() {
        return String.format("BBO[合约=%s, 买价=%s(%s), 卖价=%s(%s), 价差=%s, 时间=%d]",
            contractCode,
            bestBid, bestBidVolume,
            bestAsk, bestAskVolume,
            getSpread(),
            timestamp);
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;

        BestBidOfferEvent that = (BestBidOfferEvent) obj;

        return timestamp == that.timestamp &&
               updateSequence == that.updateSequence &&
               java.util.Objects.equals(contractCode, that.contractCode) &&
               java.util.Objects.equals(bestBid, that.bestBid) &&
               java.util.Objects.equals(bestAsk, that.bestAsk);
    }

    @Override
    public int hashCode() {
        return java.util.Objects.hash(contractCode, bestBid, bestAsk, timestamp, updateSequence);
    }
}
