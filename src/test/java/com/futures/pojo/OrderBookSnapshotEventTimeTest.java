package com.futures.pojo;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import static org.junit.jupiter.api.Assertions.*;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;

/**
 * 订单簿快照事件时间戳测试
 * 验证修复后的时间戳处理是否基于事件时间而非系统时间
 */
public class OrderBookSnapshotEventTimeTest {

    @Test
    @DisplayName("测试默认构造函数时间戳初始化")
    public void testDefaultConstructorTimestamp() {
        String contractCode = "EB2504";
        OrderBookSnapshot snapshot = new OrderBookSnapshot(contractCode);
        
        // 验证默认构造函数将时间戳初始化为0，而非系统时间
        assertEquals(0L, snapshot.getTimestamp(), 
            "默认构造函数应该将时间戳初始化为0，由调用方设置事件时间");
        assertEquals(contractCode, snapshot.getContract_cde());
        assertNotNull(snapshot.getBids());
        assertNotNull(snapshot.getAsks());
    }

    @Test
    @DisplayName("测试基于事件时间的构造函数")
    public void testEventTimeConstructor() {
        String contractCode = "EB2504";
        
        // 模拟事件时间：2025-03-17 09:30:15.500
        LocalDateTime eventDateTime = LocalDateTime.of(2025, 3, 17, 9, 30, 15, 500_000_000);
        long eventTimestamp = eventDateTime.atZone(ZoneId.of("Asia/Shanghai")).toInstant().toEpochMilli();
        
        OrderBookSnapshot snapshot = new OrderBookSnapshot(contractCode, eventTimestamp);
        
        // 验证事件时间戳设置正确
        assertEquals(eventTimestamp, snapshot.getTimestamp(), 
            "基于事件时间的构造函数应该正确设置事件时间戳");
        assertEquals(contractCode, snapshot.getContract_cde());
        
        // 验证时间戳不是系统时间
        long systemTime = System.currentTimeMillis();
        assertTrue(Math.abs(snapshot.getTimestamp() - systemTime) > 1000, 
            "快照时间戳应该是事件时间，而非系统时间");
    }

    @Test
    @DisplayName("测试时间戳设置方法")
    public void testTimestampSetter() {
        String contractCode = "EB2505";
        OrderBookSnapshot snapshot = new OrderBookSnapshot(contractCode);
        
        // 模拟事件时间：2025-03-17 14:25:30.750
        LocalDateTime eventDateTime = LocalDateTime.of(2025, 3, 17, 14, 25, 30, 750_000_000);
        long eventTimestamp = eventDateTime.atZone(ZoneId.of("Asia/Shanghai")).toInstant().toEpochMilli();
        
        snapshot.setTimestamp(eventTimestamp);
        
        assertEquals(eventTimestamp, snapshot.getTimestamp(), 
            "setTimestamp方法应该正确设置事件时间戳");
    }

    @Test
    @DisplayName("测试0.5秒间隔的事件时间戳")
    public void testHalfSecondIntervalTimestamps() {
        String contractCode = "EB2506";
        
        // 测试0.5秒间隔的时间戳序列
        long baseTime = LocalDateTime.of(2025, 3, 17, 9, 30, 0)
            .atZone(ZoneId.of("Asia/Shanghai")).toInstant().toEpochMilli();
        
        for (int i = 0; i < 10; i++) {
            long eventTime = baseTime + (i * 500); // 每0.5秒一个快照
            OrderBookSnapshot snapshot = new OrderBookSnapshot(contractCode, eventTime);
            
            assertEquals(eventTime, snapshot.getTimestamp(), 
                String.format("第%d个快照的时间戳应该正确设置为事件时间", i + 1));
            
            // 验证时间间隔
            if (i > 0) {
                long previousTime = baseTime + ((i - 1) * 500);
                assertEquals(500L, eventTime - previousTime, 
                    "相邻快照的时间间隔应该是500毫秒");
            }
        }
    }

    @Test
    @DisplayName("测试乱序事件时间戳处理")
    public void testOutOfOrderEventTimestamps() {
        String contractCode = "EB2507";
        
        // 模拟乱序到达的事件时间戳
        long[] eventTimes = {
            1710648615500L, // 09:30:15.500
            1710648615000L, // 09:30:15.000 (较早)
            1710648616000L, // 09:30:16.000 (较晚)
            1710648615750L  // 09:30:15.750 (中间)
        };
        
        for (long eventTime : eventTimes) {
            OrderBookSnapshot snapshot = new OrderBookSnapshot(contractCode, eventTime);
            assertEquals(eventTime, snapshot.getTimestamp(), 
                "即使事件乱序到达，快照也应该保持正确的事件时间戳");
        }
    }

    @Test
    @DisplayName("测试时间戳格式化输出")
    public void testTimestampFormatting() {
        String contractCode = "EB2508";
        
        // 创建具有特定事件时间的快照
        LocalDateTime eventDateTime = LocalDateTime.of(2025, 3, 17, 9, 30, 15, 500_000_000);
        long eventTimestamp = eventDateTime.atZone(ZoneId.of("Asia/Shanghai")).toInstant().toEpochMilli();
        
        OrderBookSnapshot snapshot = new OrderBookSnapshot(contractCode, eventTimestamp);
        
        // 验证时间戳可以正确格式化
        String formattedTime = LocalDateTime.ofInstant(
            java.time.Instant.ofEpochMilli(snapshot.getTimestamp()),
            ZoneId.of("Asia/Shanghai")
        ).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS"));
        
        assertEquals("2025-03-17 09:30:15.500", formattedTime, 
            "事件时间戳应该能够正确格式化为可读时间");
    }

    @Test
    @DisplayName("测试最优买卖价获取")
    public void testBestPricesWithEventTime() {
        String contractCode = "EB2509";
        long eventTimestamp = System.currentTimeMillis();
        
        OrderBookSnapshot snapshot = new OrderBookSnapshot(contractCode, eventTimestamp);
        
        // 添加买卖盘数据
        snapshot.getBids().put(2500.0, 100L);
        snapshot.getBids().put(2499.5, 200L);
        snapshot.getAsks().put(2500.5, 150L);
        snapshot.getAsks().put(2501.0, 250L);
        
        // 验证最优价格获取
        assertEquals(Double.valueOf(2500.0), snapshot.getBestBid(), 
            "应该正确获取最优买价");
        assertEquals(Double.valueOf(2500.5), snapshot.getBestAsk(), 
            "应该正确获取最优卖价");
        
        // 验证时间戳保持不变
        assertEquals(eventTimestamp, snapshot.getTimestamp(), 
            "添加订单簿数据后，事件时间戳应该保持不变");
    }
}
