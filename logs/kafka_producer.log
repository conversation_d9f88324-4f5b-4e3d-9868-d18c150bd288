2025-08-21 00:10:15,186 - kafka.conn - INFO - <BrokerConnection client_id=kafka-python-producer-1, node_id=bootstrap-0 host=localhost:9092 <connecting> [IPv6 ('::1', 9092, 0, 0)]>: connecting to localhost:9092 [('::1', 9092, 0, 0) IPv6]
2025-08-21 00:10:15,191 - kafka.conn - INFO - <BrokerConnection client_id=kafka-python-producer-1, node_id=bootstrap-0 host=localhost:9092 <checking_api_versions_recv> [IPv6 ('::1', 9092, 0, 0)]>: Broker version identified as 2.6
2025-08-21 00:10:15,192 - kafka.conn - INFO - <BrokerConnection client_id=kafka-python-producer-1, node_id=bootstrap-0 host=localhost:9092 <connected> [IPv6 ('::1', 9092, 0, 0)]>: Connection complete.
2025-08-21 00:10:15,195 - __main__ - INFO - �ɹ����ӵ�Kafka��Ⱥ: localhost:9092
2025-08-21 00:10:15,195 - __main__ - INFO - ======== ��ʼ�����ڻ�ϵͳ�������� ========
2025-08-21 00:10:15,197 - __main__ - INFO - �ɹ�����JSON�ļ�: ./data\single_leg_orders.json, ��¼��: 73
2025-08-21 00:10:15,198 - __main__ - INFO - ��ʼ���͵��ȶ������ݵ�Topic: singleleg_order_data_event
2025-08-21 00:10:15,300 - kafka.conn - INFO - <BrokerConnection client_id=kafka-python-producer-1, node_id=1 host=localhost:9092 <connecting> [IPv6 ('::1', 9092, 0, 0)]>: connecting to localhost:9092 [('::1', 9092, 0, 0) IPv6]
2025-08-21 00:10:15,300 - kafka.conn - INFO - <BrokerConnection client_id=kafka-python-producer-1, node_id=1 host=localhost:9092 <connected> [IPv6 ('::1', 9092, 0, 0)]>: Connection complete.
2025-08-21 00:10:15,301 - kafka.conn - INFO - <BrokerConnection client_id=kafka-python-producer-1, node_id=bootstrap-0 host=localhost:9092 <connected> [IPv6 ('::1', 9092, 0, 0)]>: Closing connection. 
2025-08-21 00:10:24,336 - __main__ - INFO - ���ȶ����������: �ɹ� 73/73
2025-08-21 00:10:24,336 - __main__ - INFO - ��ɷ��� single_leg_orders.json: 73 ����¼
2025-08-21 00:10:25,337 - __main__ - INFO - �ɹ�����JSON�ļ�: ./data\combination_orders.json, ��¼��: 15
2025-08-21 00:10:25,337 - __main__ - INFO - ��ʼ������϶������ݵ�Topic: cmb_order_data_event
2025-08-21 00:10:27,199 - __main__ - INFO - ��϶����������: �ɹ� 15/15
2025-08-21 00:10:27,199 - __main__ - INFO - ��ɷ��� combination_orders.json: 15 ����¼
2025-08-21 00:10:28,201 - __main__ - INFO - �ɹ�����JSON�ļ�: ./data\trades.json, ��¼��: 44
2025-08-21 00:10:28,201 - __main__ - INFO - ��ʼ���ͽ������ݵ�Topic: trade_data_event
2025-08-21 00:10:33,532 - kafka.cluster - WARNING - Topic trade_data_event is not available during auto-create initialization
2025-08-21 00:10:33,735 - kafka.cluster - WARNING - Topic trade_data_event is not available during auto-create initialization
2025-08-21 00:10:33,904 - kafka.cluster - WARNING - Topic trade_data_event is not available during auto-create initialization
2025-08-21 00:10:34,038 - kafka.cluster - WARNING - Topic trade_data_event is not available during auto-create initialization
2025-08-21 00:10:34,146 - kafka.cluster - WARNING - Topic trade_data_event is not available during auto-create initialization
2025-08-21 00:10:34,299 - kafka.cluster - WARNING - Topic trade_data_event is not available during auto-create initialization
2025-08-21 00:10:34,421 - kafka.cluster - WARNING - Topic trade_data_event is not available during auto-create initialization
2025-08-21 00:10:34,663 - kafka.cluster - WARNING - Topic trade_data_event is not available during auto-create initialization
2025-08-21 00:10:34,786 - kafka.cluster - WARNING - Topic trade_data_event is not available during auto-create initialization
2025-08-21 00:10:34,895 - kafka.cluster - WARNING - Topic trade_data_event is not available during auto-create initialization
2025-08-21 00:10:35,022 - kafka.cluster - WARNING - Topic trade_data_event is not available during auto-create initialization
2025-08-21 00:10:35,129 - kafka.cluster - WARNING - Topic trade_data_event is not available during auto-create initialization
2025-08-21 00:10:35,257 - kafka.cluster - WARNING - Topic trade_data_event is not available during auto-create initialization
2025-08-21 00:10:35,390 - kafka.cluster - WARNING - Topic trade_data_event is not available during auto-create initialization
2025-08-21 00:10:35,538 - kafka.cluster - WARNING - Topic trade_data_event is not available during auto-create initialization
2025-08-21 00:10:35,653 - kafka.cluster - WARNING - Topic trade_data_event is not available during auto-create initialization
2025-08-21 00:10:35,760 - kafka.cluster - WARNING - Topic trade_data_event is not available during auto-create initialization
2025-08-21 00:10:35,898 - kafka.cluster - WARNING - Topic trade_data_event is not available during auto-create initialization
2025-08-21 00:10:36,009 - kafka.cluster - WARNING - Topic trade_data_event is not available during auto-create initialization
2025-08-21 00:10:36,119 - kafka.cluster - WARNING - Topic trade_data_event is not available during auto-create initialization
2025-08-21 00:10:36,226 - kafka.cluster - WARNING - Topic trade_data_event is not available during auto-create initialization
2025-08-21 00:10:36,357 - kafka.cluster - WARNING - Topic trade_data_event is not available during auto-create initialization
2025-08-21 00:10:36,484 - kafka.cluster - WARNING - Topic trade_data_event is not available during auto-create initialization
2025-08-21 00:10:36,600 - kafka.cluster - WARNING - Topic trade_data_event is not available during auto-create initialization
2025-08-21 00:10:36,714 - kafka.cluster - WARNING - Topic trade_data_event is not available during auto-create initialization
2025-08-21 00:10:36,854 - kafka.cluster - WARNING - Topic trade_data_event is not available during auto-create initialization
2025-08-21 00:10:36,973 - kafka.cluster - WARNING - Topic trade_data_event is not available during auto-create initialization
2025-08-21 00:10:37,083 - kafka.cluster - WARNING - Topic trade_data_event is not available during auto-create initialization
2025-08-21 00:10:37,191 - kafka.cluster - WARNING - Topic trade_data_event is not available during auto-create initialization
2025-08-21 00:10:37,373 - kafka.cluster - WARNING - Topic trade_data_event is not available during auto-create initialization
2025-08-21 00:10:37,484 - kafka.cluster - WARNING - Topic trade_data_event is not available during auto-create initialization
2025-08-21 00:10:37,593 - kafka.cluster - WARNING - Topic trade_data_event is not available during auto-create initialization
2025-08-21 00:10:37,704 - kafka.cluster - WARNING - Topic trade_data_event is not available during auto-create initialization
2025-08-21 00:10:37,812 - kafka.cluster - WARNING - Topic trade_data_event is not available during auto-create initialization
2025-08-21 00:10:37,921 - kafka.cluster - WARNING - Topic trade_data_event is not available during auto-create initialization
2025-08-21 00:10:38,031 - kafka.cluster - WARNING - Topic trade_data_event is not available during auto-create initialization
2025-08-21 00:10:38,139 - kafka.cluster - WARNING - Topic trade_data_event is not available during auto-create initialization
2025-08-21 00:10:38,267 - kafka.cluster - WARNING - Topic trade_data_event is not available during auto-create initialization
2025-08-21 00:10:38,372 - kafka.cluster - WARNING - Topic trade_data_event is not available during auto-create initialization
2025-08-21 00:10:38,550 - kafka.cluster - WARNING - Topic trade_data_event is not available during auto-create initialization
2025-08-21 00:10:44,099 - __main__ - INFO - �������ݷ������: �ɹ� 44/44
2025-08-21 00:10:44,099 - __main__ - INFO - ��ɷ��� trades.json: 44 ����¼
2025-08-21 00:10:45,099 - __main__ - INFO - ======== ���ݷ�����ɣ��ܼ�: 132 ����¼ ========
2025-08-21 00:10:45,099 - kafka.producer.kafka - INFO - <KafkaProducer client_id=kafka-python-producer-1 transactional_id=None>: Closing the Kafka producer with 4294967.0 secs timeout.
2025-08-21 00:10:45,101 - kafka.conn - INFO - <BrokerConnection client_id=kafka-python-producer-1, node_id=1 host=localhost:9092 <connected> [IPv6 ('::1', 9092, 0, 0)]>: Closing connection. 
2025-08-21 00:10:45,101 - __main__ - INFO - Kafka�������ѹر�
2025-08-21 00:12:43,904 - kafka.conn - INFO - <BrokerConnection client_id=kafka-python-producer-1, node_id=bootstrap-0 host=localhost:9092 <connecting> [IPv6 ('::1', 9092, 0, 0)]>: connecting to localhost:9092 [('::1', 9092, 0, 0) IPv6]
2025-08-21 00:12:43,954 - kafka.conn - INFO - <BrokerConnection client_id=kafka-python-producer-1, node_id=bootstrap-0 host=localhost:9092 <checking_api_versions_recv> [IPv6 ('::1', 9092, 0, 0)]>: Broker version identified as 2.6
2025-08-21 00:12:43,955 - kafka.conn - INFO - <BrokerConnection client_id=kafka-python-producer-1, node_id=bootstrap-0 host=localhost:9092 <connected> [IPv6 ('::1', 9092, 0, 0)]>: Connection complete.
2025-08-21 00:12:43,956 - __main__ - INFO - �ɹ����ӵ�Kafka��Ⱥ: localhost:9092
2025-08-21 00:12:43,957 - __main__ - INFO - ======== ��ʼ�����ڻ�ϵͳ�������� ========
2025-08-21 00:12:43,959 - __main__ - INFO - �ɹ�����JSON�ļ�: ./data\single_leg_orders.json, ��¼��: 73
2025-08-21 00:12:43,959 - __main__ - INFO - ��ʼ���͵��ȶ������ݵ�Topic: singleleg_order_data_event
2025-08-21 00:12:44,065 - kafka.conn - INFO - <BrokerConnection client_id=kafka-python-producer-1, node_id=1 host=localhost:9092 <connecting> [IPv6 ('::1', 9092, 0, 0)]>: connecting to localhost:9092 [('::1', 9092, 0, 0) IPv6]
2025-08-21 00:12:44,066 - kafka.conn - INFO - <BrokerConnection client_id=kafka-python-producer-1, node_id=1 host=localhost:9092 <connected> [IPv6 ('::1', 9092, 0, 0)]>: Connection complete.
2025-08-21 00:12:44,066 - kafka.conn - INFO - <BrokerConnection client_id=kafka-python-producer-1, node_id=bootstrap-0 host=localhost:9092 <connected> [IPv6 ('::1', 9092, 0, 0)]>: Closing connection. 
2025-08-21 00:12:53,082 - __main__ - INFO - ���ȶ����������: �ɹ� 73/73
2025-08-21 00:12:53,082 - __main__ - INFO - ��ɷ��� single_leg_orders.json: 73 ����¼
2025-08-21 00:12:54,083 - __main__ - INFO - �ɹ�����JSON�ļ�: ./data\combination_orders.json, ��¼��: 15
2025-08-21 00:12:54,084 - __main__ - INFO - ��ʼ������϶������ݵ�Topic: cmb_order_data_event
2025-08-21 00:12:55,930 - __main__ - INFO - ��϶����������: �ɹ� 15/15
2025-08-21 00:12:55,930 - __main__ - INFO - ��ɷ��� combination_orders.json: 15 ����¼
2025-08-21 00:12:56,933 - __main__ - INFO - �ɹ�����JSON�ļ�: ./data\trades.json, ��¼��: 44
2025-08-21 00:12:56,933 - __main__ - INFO - ��ʼ���ͽ������ݵ�Topic: trade_data_event
2025-08-21 00:13:02,380 - __main__ - INFO - �������ݷ������: �ɹ� 44/44
2025-08-21 00:13:02,380 - __main__ - INFO - ��ɷ��� trades.json: 44 ����¼
2025-08-21 00:13:03,381 - __main__ - INFO - ======== ���ݷ�����ɣ��ܼ�: 132 ����¼ ========
2025-08-21 00:13:03,381 - kafka.producer.kafka - INFO - <KafkaProducer client_id=kafka-python-producer-1 transactional_id=None>: Closing the Kafka producer with 4294967.0 secs timeout.
2025-08-21 00:13:03,382 - kafka.conn - INFO - <BrokerConnection client_id=kafka-python-producer-1, node_id=1 host=localhost:9092 <connected> [IPv6 ('::1', 9092, 0, 0)]>: Closing connection. 
2025-08-21 00:13:03,382 - __main__ - INFO - Kafka�������ѹر�
